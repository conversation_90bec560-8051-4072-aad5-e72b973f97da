---
export interface Props {
  title: string;
  description?: string;
  image?: string;
  type?: 'website' | 'article';
}

const { title, description = 'Senior Backend Developer & Technical Leader specializing in Java, Spring Boot, AWS, and team leadership.', image = '/og-image.jpg', type = 'website' } = Astro.props;

const canonicalURL = new URL(Astro.url.pathname, Astro.site);
const socialImageURL = new URL(image, Astro.url);
---

<!-- Primary Meta Tags -->
<title>{title}</title>
<meta name="title" content={title} />
<meta name="description" content={description} />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta charset="UTF-8" />

<!-- Canonical URL -->
<link rel="canonical" href={canonicalURL} />

<!-- Open Graph / Facebook -->
<meta property="og:type" content={type} />
<meta property="og:url" content={Astro.url} />
<meta property="og:title" content={title} />
<meta property="og:description" content={description} />
<meta property="og:image" content={socialImageURL} />

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image" />
<meta property="twitter:url" content={Astro.url} />
<meta property="twitter:title" content={title} />
<meta property="twitter:description" content={description} />
<meta property="twitter:image" content={socialImageURL} />

<!-- Favicon -->
<link rel="icon" type="image/svg+xml" href="/favicon.svg" />

<!-- Structured Data -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Person",
  "name": "Your Name",
  "jobTitle": "Senior Backend Developer",
  "description": "Backend Engineer, Technical Leader, and System Architect",
  "url": "https://your-portfolio.vercel.app",
  "sameAs": [
    "https://github.com/yourusername",
    "https://linkedin.com/in/yourusername"
  ],
  "knowsAbout": [
    "Java", "Spring Boot", "AWS", "Docker", "MySQL", "MongoDB", "Redis", "Team Leadership"
  ]
}
</script>
