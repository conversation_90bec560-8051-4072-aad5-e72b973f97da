---
import { getCollection } from 'astro:content';

const blogPosts = await getCollection('blog', ({ data }) => {
  return !data.draft;
});

const sortedPosts = blogPosts
  .sort((a, b) => b.data.publishDate.valueOf() - a.data.publishDate.valueOf())
  .slice(0, 6);

const categories = ['All', 'Backend', 'DevOps', 'Leadership', 'Architecture'];
---

<section id="blog" class="py-20 bg-white dark:bg-dark-900">
  <div class="container mx-auto px-4">
    <div class="max-w-6xl mx-auto">
      <div class="text-center mb-16 fade-in-section">
        <h2 class="text-4xl md:text-5xl font-heading font-bold mb-6 text-gray-900 dark:text-white">
          Latest Blog Posts
        </h2>
        <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Sharing insights on backend development, system architecture, and technical leadership
        </p>
      </div>

      <!-- Category Filter -->
      <div class="flex flex-wrap justify-center gap-4 mb-12 fade-in-section">
        {categories.map((category) => (
          <button 
            class="category-filter px-6 py-2 rounded-full border-2 border-primary-200 dark:border-primary-800 text-primary-600 dark:text-primary-400 hover:bg-primary-500 hover:text-white hover:border-primary-500 transition-all duration-300"
            data-category={category.toLowerCase()}
          >
            {category}
          </button>
        ))}
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {sortedPosts.map((post) => (
          <article class={`blog-card fade-in-section ${post.data.featured ? 'featured' : ''}`} data-category={post.data.category.toLowerCase()}>
            <div class="bg-white dark:bg-dark-800 rounded-2xl shadow-lg border border-gray-100 dark:border-dark-700 overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-2">
              <div class="p-6">
                <div class="flex items-center gap-4 mb-4">
                  <span class="px-3 py-1 bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 rounded-full text-sm font-medium">
                    {post.data.category}
                  </span>
                  <time class="text-gray-500 dark:text-gray-400 text-sm">
                    {post.data.publishDate.toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </time>
                </div>
                
                <h3 class="text-xl font-heading font-semibold mb-3 text-gray-900 dark:text-white line-clamp-2">
                  <a href={`/blog/${post.slug}`} class="hover:text-primary-500 transition-colors">
                    {post.data.title}
                  </a>
                </h3>
                
                <p class="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                  {post.data.description}
                </p>
                
                <div class="flex flex-wrap gap-2 mb-4">
                  {post.data.tags.slice(0, 3).map((tag) => (
                    <span class="px-2 py-1 bg-gray-100 dark:bg-dark-700 text-gray-600 dark:text-gray-400 rounded text-xs">
                      #{tag}
                    </span>
                  ))}
                </div>
                
                <a 
                  href={`/blog/${post.slug}`}
                  class="inline-flex items-center gap-2 text-primary-500 hover:text-primary-600 font-medium transition-colors"
                >
                  Read More
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </a>
              </div>
            </div>
          </article>
        ))}
      </div>

      <div class="text-center mt-12 fade-in-section">
        <a href="/blog" class="btn-primary">
          View All Posts
        </a>
      </div>
    </div>
  </div>
</section>

<script>
  // Category filtering
  document.addEventListener('DOMContentLoaded', () => {
    const filterButtons = document.querySelectorAll('.category-filter');
    const blogCards = document.querySelectorAll('.blog-card');

    filterButtons.forEach(button => {
      button.addEventListener('click', () => {
        const category = button.getAttribute('data-category');
        
        // Update active button
        filterButtons.forEach(btn => btn.classList.remove('bg-primary-500', 'text-white', 'border-primary-500'));
        button.classList.add('bg-primary-500', 'text-white', 'border-primary-500');
        
        // Filter cards
        blogCards.forEach(card => {
          const cardCategory = card.getAttribute('data-category');
          if (category === 'all' || cardCategory === category) {
            card.style.display = 'block';
            card.classList.add('animate-fade-in');
          } else {
            card.style.display = 'none';
          }
        });
      });
    });

    // Set initial active state
    filterButtons[0].classList.add('bg-primary-500', 'text-white', 'border-primary-500');
  });
</script>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .blog-card.featured {
    @apply ring-2 ring-accent-200 dark:ring-accent-800;
  }
</style>
