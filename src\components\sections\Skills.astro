---
const skillsData = [
  {
    category: "Backend Development",
    skills: [
      { name: "Java", level: 95 },
      { name: "Groovy", level: 85 },
      { name: "Grails", level: 80 },
      { name: "Spring Boot", level: 90 },
      { name: "Node.js", level: 75 },
      { name: "Python", level: 70 }
    ]
  },
  {
    category: "Databases",
    skills: [
      { name: "MySQL", level: 90 },
      { name: "MongoDB", level: 85 },
      { name: "Redis", level: 80 },
      { name: "PostgreSQL", level: 85 },
      { name: "Elasticsearch", level: 75 }
    ]
  },
  {
    category: "DevOps & Cloud",
    skills: [
      { name: "A<PERSON>", level: 85 },
      { name: "<PERSON><PERSON>", level: 90 },
      { name: "Kubernetes", level: 75 },
      { name: "Linux", level: 85 },
      { name: "CI/CD", level: 80 },
      { name: "Terraform", level: 70 }
    ]
  },
  {
    category: "Leadership & Soft Skills",
    skills: [
      { name: "Team Leading", level: 90 },
      { name: "Mentoring", level: 85 },
      { name: "Project Management", level: 80 },
      { name: "Technical Writing", level: 85 },
      { name: "Code Review", level: 90 }
    ]
  }
];
---

<section id="skills" class="py-20 bg-gray-50 dark:bg-dark-800">
  <div class="container mx-auto px-4">
    <div class="max-w-6xl mx-auto">
      <div class="text-center mb-16 fade-in-section">
        <h2 class="text-4xl md:text-5xl font-heading font-bold mb-6 text-gray-900 dark:text-white">
          Skills & Expertise
        </h2>
        <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          A comprehensive overview of my technical skills and leadership capabilities
        </p>
      </div>

      <div class="grid md:grid-cols-2 gap-8">
        {skillsData.map((category, categoryIndex) => (
          <div class="fade-in-section bg-white dark:bg-dark-900 rounded-2xl p-8 shadow-lg">
            <h3 class="text-2xl font-heading font-semibold mb-6 text-gray-900 dark:text-white">
              {category.category}
            </h3>
            <div class="space-y-4">
              {category.skills.map((skill, skillIndex) => (
                <div class="skill-item">
                  <div class="flex justify-between items-center mb-2">
                    <span class="font-medium text-gray-700 dark:text-gray-300">{skill.name}</span>
                    <span class="text-sm text-gray-500 dark:text-gray-400">{skill.level}%</span>
                  </div>
                  <div class="w-full bg-gray-200 dark:bg-dark-700 rounded-full h-2">
                    <div 
                      class="skill-bar bg-gradient-to-r from-primary-500 to-accent-500 h-2 rounded-full transition-all duration-1000 ease-out"
                      style={`width: 0%`}
                      data-width={skill.level}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
</section>

<script>
  // Animate skill bars when they come into view
  const observerOptions = {
    threshold: 0.5,
    rootMargin: '0px 0px -100px 0px'
  };

  const skillObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const skillBars = entry.target.querySelectorAll('.skill-bar');
        skillBars.forEach(bar => {
          const width = bar.getAttribute('data-width');
          setTimeout(() => {
            bar.style.width = width + '%';
          }, 200);
        });
      }
    });
  }, observerOptions);

  document.addEventListener('DOMContentLoaded', () => {
    const skillSections = document.querySelectorAll('.fade-in-section');
    skillSections.forEach(section => {
      if (section.querySelector('.skill-bar')) {
        skillObserver.observe(section);
      }
    });
  });
</script>
