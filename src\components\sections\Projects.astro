---
const projects = [
  {
    title: "E-commerce API Platform",
    description: "Scalable microservices-based e-commerce API handling 50M+ requests daily with real-time inventory management and payment processing.",
    image: "/placeholder.svg?height=300&width=400",
    technologies: ["Java", "Spring Boot", "MySQL", "Redis", "AWS", "Docker"],
    github: "https://github.com/yourusername/ecommerce-api",
    demo: "https://api-demo.example.com",
    featured: true
  },
  {
    title: "Real-time Chat System",
    description: "High-performance chat application with WebSocket support, message queuing, and horizontal scaling capabilities.",
    image: "/placeholder.svg?height=300&width=400",
    technologies: ["Java", "Spring WebSocket", "MongoDB", "RabbitMQ", "Docker"],
    github: "https://github.com/yourusername/chat-system",
    featured: true
  },
  {
    title: "DevOps Pipeline Automation",
    description: "Complete CI/CD pipeline with automated testing, deployment, and monitoring for microservices architecture.",
    image: "/placeholder.svg?height=300&width=400",
    technologies: ["Jenkins", "Docker", "Kubernetes", "AWS", "Terraform", "Prometheus"],
    github: "https://github.com/yourusername/devops-pipeline",
    featured: true
  },
  {
    title: "Database Migration Tools",
    description: "Custom database migration and synchronization tools for large-scale data operations with zero downtime.",
    image: "/placeholder.svg?height=300&width=400",
    technologies: ["Java", "Spring Batch", "MySQL", "PostgreSQL", "Flyway"],
    github: "https://github.com/yourusername/db-migration-tools",
    featured: false
  },
  {
    title: "Monitoring Dashboard",
    description: "Real-time system monitoring dashboard with custom metrics, alerting, and performance analytics.",
    image: "/placeholder.svg?height=300&width=400",
    technologies: ["Java", "Spring Boot", "Grafana", "Prometheus", "InfluxDB"],
    github: "https://github.com/yourusername/monitoring-dashboard",
    demo: "https://monitoring-demo.example.com",
    featured: false
  },
  {
    title: "Team Management Platform",
    description: "Internal platform for team management, performance tracking, and resource allocation with role-based access control.",
    image: "/placeholder.svg?height=300&width=400",
    technologies: ["Java", "Spring Security", "React", "PostgreSQL", "AWS"],
    github: "https://github.com/yourusername/team-management",
    featured: false
  }
];
---

<section id="projects" class="py-20 bg-gray-50 dark:bg-dark-800">
  <div class="container mx-auto px-4">
    <div class="max-w-6xl mx-auto">
      <div class="text-center mb-16 fade-in-section">
        <h2 class="text-4xl md:text-5xl font-heading font-bold mb-6 text-gray-900 dark:text-white">
          Featured Projects
        </h2>
        <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          A showcase of my recent work and technical achievements
        </p>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {projects.map((project, index) => (
          <div class={`project-card fade-in-section ${project.featured ? 'featured' : ''}`}>
            <div class="relative overflow-hidden rounded-t-2xl">
              <img 
                src={project.image || "/placeholder.svg"} 
                alt={project.title}
                class="w-full h-48 object-cover transition-transform duration-300 hover:scale-110"
                loading="lazy"
              />
              {project.featured && (
                <div class="absolute top-4 right-4 bg-accent-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                  Featured
                </div>
              )}
            </div>
            
            <div class="p-6">
              <h3 class="text-xl font-heading font-semibold mb-3 text-gray-900 dark:text-white">
                {project.title}
              </h3>
              
              <p class="text-gray-600 dark:text-gray-300 mb-4 leading-relaxed">
                {project.description}
              </p>
              
              <div class="flex flex-wrap gap-2 mb-6">
                {project.technologies.map((tech) => (
                  <span class="px-2 py-1 bg-gray-100 dark:bg-dark-700 text-gray-700 dark:text-gray-300 rounded text-sm">
                    {tech}
                  </span>
                ))}
              </div>
              
              <div class="flex gap-4">
                {project.github && (
                  <a 
                    href={project.github} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    class="flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                  >
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                    </svg>
                    Code
                  </a>
                )}
                {project.demo && (
                  <a 
                    href={project.demo} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    class="flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                  >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                    </svg>
                    Demo
                  </a>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
</section>

<style>
  .project-card {
    @apply bg-white dark:bg-dark-900 rounded-2xl shadow-lg border border-gray-100 dark:border-dark-700 transition-all duration-300 hover:shadow-xl hover:-translate-y-2;
  }
  
  .project-card.featured {
    @apply ring-2 ring-primary-200 dark:ring-primary-800;
  }
  
  .dark .project-card:hover {
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  }
</style>
