export interface Experience {
  company: string
  position: string
  duration: string
  location: string
  description: string[]
  technologies: string[]
  achievements: string[]
}

export interface Skill {
  category: string
  skills: {
    name: string
    level: number
    icon?: string
  }[]
}

export interface Project {
  title: string
  description: string
  image: string
  technologies: string[]
  github?: string
  demo?: string
  featured: boolean
}

export interface BlogPost {
  title: string
  description: string
  publishDate: Date
  category: string
  tags: string[]
  slug: string
}
