---
---

<section class="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-dark-900 dark:to-dark-800 pt-20">
  <div class="container mx-auto px-4 text-center">
    <div class="max-w-4xl mx-auto fade-in-section">
      <h1 class="text-5xl md:text-7xl font-heading font-bold mb-6 text-gray-900 dark:text-white">
        Hi, I'm <span class="text-primary-500">Your Name</span>
      </h1>
      
      <div class="text-2xl md:text-3xl font-medium mb-8 h-16 flex items-center justify-center">
        <span class="typewriter-container">
          <span id="typewriter" class="border-r-2 border-accent-500 animate-blink"></span>
        </span>
      </div>
      
      <p class="text-xl text-gray-600 dark:text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed">
        Senior Backend Developer with 8+ years of experience building scalable systems, 
        leading high-performing teams, and architecting robust solutions using Java, Spring Boot, 
        AWS, and modern DevOps practices.
      </p>
      
      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <a href="#contact" class="btn-primary">
          Get In Touch
        </a>
        <a href="#projects" class="btn-secondary">
          View My Work
        </a>
      </div>
      
      <div class="mt-16 flex justify-center space-x-6">
        <a href="https://github.com/yourusername" target="_blank" rel="noopener noreferrer" class="social-link" aria-label="GitHub">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
          </svg>
        </a>
        <a href="https://linkedin.com/in/yourusername" target="_blank" rel="noopener noreferrer" class="social-link" aria-label="LinkedIn">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
          </svg>
        </a>
        <a href="mailto:<EMAIL>" class="social-link" aria-label="Email">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
          </svg>
        </a>
      </div>
    </div>
  </div>
</section>

<script>
  const phrases = [
    "Backend Engineer",
    "Technical Leader", 
    "System Architect",
    "Team Mentor"
  ];
  
  let currentPhrase = 0;
  let currentChar = 0;
  let isDeleting = false;
  
  function typeWriter() {
    const typewriterElement = document.getElementById('typewriter');
    if (!typewriterElement) return;
    
    const current = phrases[currentPhrase];
    
    if (isDeleting) {
      typewriterElement.textContent = current.substring(0, currentChar - 1);
      currentChar--;
    } else {
      typewriterElement.textContent = current.substring(0, currentChar + 1);
      currentChar++;
    }
    
    let typeSpeed = isDeleting ? 50 : 100;
    
    if (!isDeleting && currentChar === current.length) {
      typeSpeed = 2000;
      isDeleting = true;
    } else if (isDeleting && currentChar === 0) {
      isDeleting = false;
      currentPhrase = (currentPhrase + 1) % phrases.length;
      typeSpeed = 500;
    }
    
    setTimeout(typeWriter, typeSpeed);
  }
  
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(typeWriter, 1000);
  });
</script>
