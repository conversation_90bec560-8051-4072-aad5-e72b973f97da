---
const experiences = [
  {
    company: "Tech Solutions Inc.",
    position: "Senior Backend Engineer & Team Lead",
    duration: "2021 - Present",
    location: "San Francisco, CA",
    description: [
      "Lead a team of 8 backend developers in designing and implementing microservices architecture",
      "Architected and developed high-performance APIs serving 10M+ requests daily",
      "Implemented CI/CD pipelines reducing deployment time by 60%"
    ],
    technologies: ["Java", "Spring Boot", "AWS", "Docker", "MySQL", "Redis"],
    achievements: [
      "Reduced system latency by 45% through database optimization",
      "Mentored 5 junior developers, 3 of whom were promoted",
      "Led migration from monolith to microservices architecture"
    ]
  },
  {
    company: "Digital Innovations Corp",
    position: "Backend Developer",
    duration: "2019 - 2021",
    location: "Austin, TX",
    description: [
      "Developed and maintained RESTful APIs using Java and Spring Boot",
      "Optimized database queries resulting in 30% performance improvement",
      "Collaborated with frontend teams to deliver seamless user experiences"
    ],
    technologies: ["Java", "Spring", "PostgreSQL", "Docker", "AWS"],
    achievements: [
      "Built real-time notification system handling 1M+ messages daily",
      "Implemented automated testing increasing code coverage to 85%",
      "Reduced API response time by 40% through caching strategies"
    ]
  },
  {
    company: "StartupXYZ",
    position: "Full Stack Developer",
    duration: "2017 - 2019",
    location: "Remote",
    description: [
      "Built full-stack applications using Java, Spring, and React",
      "Designed database schemas and implemented data migration strategies",
      "Worked directly with stakeholders to gather requirements and deliver solutions"
    ],
    technologies: ["Java", "Spring", "React", "MySQL", "Linux"],
    achievements: [
      "Developed MVP that secured $2M in Series A funding",
      "Implemented payment processing system with 99.9% uptime",
      "Built admin dashboard reducing manual work by 70%"
    ]
  }
];
---

<section id="experience" class="py-20 bg-white dark:bg-dark-900">
  <div class="container mx-auto px-4">
    <div class="max-w-6xl mx-auto">
      <div class="text-center mb-16 fade-in-section">
        <h2 class="text-4xl md:text-5xl font-heading font-bold mb-6 text-gray-900 dark:text-white">
          Professional Experience
        </h2>
        <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          My journey through various roles and the impact I've made
        </p>
      </div>

      <div class="relative">
        <!-- Timeline line -->
        <div class="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 w-0.5 h-full bg-primary-200 dark:bg-primary-800"></div>

        {experiences.map((exp, index) => (
          <div class={`relative mb-12 ${index % 2 === 0 ? 'md:pr-1/2' : 'md:pl-1/2 md:ml-auto'} fade-in-section`}>
            <!-- Timeline dot -->
            <div class="absolute left-6 md:left-1/2 transform md:-translate-x-1/2 w-4 h-4 bg-primary-500 rounded-full border-4 border-white dark:border-dark-900 z-10"></div>
            
            <div class={`ml-16 md:ml-0 ${index % 2 === 0 ? 'md:mr-8' : 'md:ml-8'}`}>
              <div class="bg-white dark:bg-dark-800 rounded-2xl p-8 shadow-lg border border-gray-100 dark:border-dark-700 experience-card">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                  <div>
                    <h3 class="text-2xl font-heading font-semibold text-gray-900 dark:text-white mb-1">
                      {exp.position}
                    </h3>
                    <h4 class="text-xl text-primary-500 font-medium mb-2">{exp.company}</h4>
                  </div>
                  <div class="text-right">
                    <div class="text-gray-600 dark:text-gray-400 font-medium">{exp.duration}</div>
                    <div class="text-gray-500 dark:text-gray-500 text-sm">{exp.location}</div>
                  </div>
                </div>

                <div class="mb-6">
                  <ul class="space-y-2">
                    {exp.description.map((desc) => (
                      <li class="text-gray-600 dark:text-gray-300 flex items-start">
                        <span class="text-primary-500 mr-2 mt-1.5 w-1.5 h-1.5 rounded-full bg-current flex-shrink-0"></span>
                        {desc}
                      </li>
                    ))}
                  </ul>
                </div>

                <div class="mb-6">
                  <h5 class="font-semibold text-gray-900 dark:text-white mb-3">Key Achievements:</h5>
                  <ul class="space-y-2">
                    {exp.achievements.map((achievement) => (
                      <li class="text-gray-600 dark:text-gray-300 flex items-start">
                        <span class="text-accent-500 mr-2 mt-1.5">✓</span>
                        {achievement}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h5 class="font-semibold text-gray-900 dark:text-white mb-3">Technologies:</h5>
                  <div class="flex flex-wrap gap-2">
                    {exp.technologies.map((tech) => (
                      <span class="px-3 py-1 bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 rounded-full text-sm font-medium">
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
</section>

<style>
  .experience-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .experience-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }
  
  .dark .experience-card:hover {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  }
</style>
