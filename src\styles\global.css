@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: "Poppins", system-ui, sans-serif;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: "Montserrat", system-ui, sans-serif;
  }

  code,
  pre {
    font-family: "Fira Code", monospace;
  }
}

@layer components {
  .btn-primary {
    @apply inline-flex items-center justify-center px-8 py-3 bg-primary-500 text-white font-medium rounded-lg hover:bg-primary-600 focus:ring-4 focus:ring-primary-200 dark:focus:ring-primary-800 transition-all duration-300 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center px-8 py-3 bg-transparent text-primary-500 font-medium rounded-lg border-2 border-primary-500 hover:bg-primary-500 hover:text-white focus:ring-4 focus:ring-primary-200 dark:focus:ring-primary-800 transition-all duration-300;
  }

  .fade-in-section {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }

  .fade-in-section.animate-fade-in {
    opacity: 1;
    transform: translateY(0);
  }

  .container {
    @apply max-w-7xl mx-auto;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-dark-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 dark:bg-dark-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500 dark:bg-dark-500;
}

/* Focus styles for accessibility */
*:focus-visible {
  @apply outline-none ring-2 ring-primary-500 ring-offset-2 dark:ring-offset-dark-900;
}
